{"openapi": "3.0.0", "info": {"title": "Sistema PDV - Supermercado da Dona Maria - API", "description": "API completa para sistema de Ponto de Venda do supermercado da Dona Maria, incluindo gestão de produtos, operações de caixa e controle de compras.", "version": "1.0.0", "contact": {"name": "Equipe de Desenvolvimento", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:5000/"}], "tags": [{"name": "Autenticação", "description": "Endpoints para autenticação de usuários"}, {"name": "<PERSON><PERSON><PERSON>", "description": "Gestão de produtos (apenas administradores)"}, {"name": "Operações de Caixa", "description": "Operações de caixa para registro de compras"}], "paths": {"/": {"get": {"description": "", "responses": {"200": {"description": "OK"}}}}, "/auth/login": {"post": {"tags": ["Autenticação"], "summary": "Realizar login do usuário", "description": "Realiza a autenticação do usuário através de email e senha, retornando um token JWT", "responses": {"200": {"description": "Login realizado com sucesso", "content": {"application/json": {"schema": {"type": "object", "properties": {"token": {"type": "string", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}}}}}}, "400": {"description": "Dados obrigatórios não fornecidos"}, "401": {"description": "Credenciais inválidas"}, "500": {"description": "Erro interno do servidor"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "senha": {"type": "string", "example": "12345"}}, "required": ["email", "<PERSON><PERSON>a"]}}}}}}, "/produtos/": {"post": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Cadastrar novo produto", "description": "Cadastra um novo produto no sistema (apenas administradores)", "parameters": [{"name": "authorization", "in": "header", "schema": {"type": "string"}}], "responses": {"201": {"description": "Produto cadastrado com sucesso", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}}}}}}, "400": {"description": "Dados obrigatórios não fornecidos"}, "401": {"description": "Token não fornecido ou inválido"}, "403": {"description": "<PERSON><PERSON> negado - apenas administradores"}, "409": {"description": "Código de barras j<PERSON> existe"}, "500": {"description": "Erro interno do servidor"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"prd_nome": {"type": "string", "example": "Coca-Cola 2L"}, "prd_valor": {"type": "number", "example": 5.99}, "prd_codigobarras": {"type": "string", "example": "7894900011517"}, "cat_id": {"type": "integer", "example": 4}}, "required": ["prd_nome", "prd_valor", "prd_codigobarras"]}}}}}}, "/produtos/{id}": {"delete": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Inativar produto", "description": "Realiza exclusão lógica do produto (apenas administradores)", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "ID do produto"}, {"name": "authorization", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "Produto inativado com sucesso"}, "400": {"description": "Bad Request"}, "401": {"description": "Token não fornecido ou inválido"}, "403": {"description": "<PERSON><PERSON> negado - apenas administradores"}, "404": {"description": "Produto não encontrado"}, "500": {"description": "Erro interno do servidor"}}, "security": [{"bearerAuth": []}]}}, "/compras/iniciar": {"post": {"tags": ["Operações de Caixa"], "summary": "Iniciar nova compra", "description": "Inicia uma nova compra no caixa", "parameters": [{"name": "authorization", "in": "header", "schema": {"type": "string"}}], "responses": {"201": {"description": "Compra iniciada com sucesso", "content": {"application/json": {"schema": {"type": "object", "properties": {"com_id": {"type": "integer", "example": 1}}}}}}, "400": {"description": "CPF inválido"}, "401": {"description": "Token não fornecido ou inválido"}, "500": {"description": "Erro interno do servidor"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": false, "content": {"application/json": {"schema": {"type": "object", "properties": {"com_cpfcliente": {"type": "string", "example": "12345678901", "description": "CPF do cliente (11 dígitos)"}, "com_cpfnanota": {"type": "string", "enum": ["S", "N"], "example": "S", "description": "Flag indicando se cliente deseja CPF na nota fiscal"}}}}}}}}, "/compras/{id}/itens": {"post": {"tags": ["Operações de Caixa"], "summary": "Adicionar item à compra", "description": "Adiciona um produto à compra pelo código de barras. Se o produto já existe, incrementa a quantidade.", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "ID da compra"}, {"name": "authorization", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "Item adicionado com sucesso"}, "400": {"description": "Código de barras é obrigatório"}, "401": {"description": "Token não fornecido ou inválido"}, "404": {"description": "Compra não encontrada, já finalizada ou produto não encontrado"}, "500": {"description": "Erro interno do servidor"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"prd_codigobarras": {"type": "string", "example": "5879628483562"}}, "required": ["prd_codigobarras"]}}}}}}, "/compras/{id}/finalizar": {"patch": {"tags": ["Operações de Caixa"], "summary": "Finalizar compra", "description": "Finaliza uma compra em andamento, calculando o valor total", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "ID da compra"}, {"name": "authorization", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "Compra finalizada com sucesso"}, "400": {"description": "Compra deve ter pelo menos um item"}, "401": {"description": "Token não fornecido ou inválido"}, "404": {"description": "Compra não encontrada ou já finalizada"}, "500": {"description": "Erro interno do servidor"}}, "security": [{"bearerAuth": []}]}}, "/compras/{id}/extrato": {"get": {"tags": ["Operações de Caixa"], "summary": "Obter extrato da compra", "description": "Obtém o extrato completo de uma compra com todos os detalhes", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "ID da compra"}, {"name": "authorization", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "Extrato obtido com sucesso", "content": {"application/json": {"schema": {"type": "object", "properties": {"compra": {"type": "object", "properties": {"com_id": {"type": "integer", "example": 1}, "com_cpfcliente": {"type": "string", "example": "12345678901"}, "com_cpfnanota": {"type": "string", "example": "S"}, "com_datainicio": {"type": "string", "example": "2024-01-15T10:30:00.000Z"}, "com_datafim": {"type": "string", "example": "2024-01-15T10:45:00.000Z"}, "com_valortotal": {"type": "number", "example": 15.5}, "operador": {"type": "string", "example": "Fulano"}}}, "itens": {"type": "array", "items": {"type": "object", "properties": {"prd_nome": {"type": "string", "example": "Bavaria Latão"}, "ico_quantidade": {"type": "integer", "example": 2}, "ico_valorunitario": {"type": "number", "example": 2.9}, "ico_valortotal": {"type": "number", "example": 5.8}}}}}}}}}, "401": {"description": "Token não fornecido ou inválido"}, "404": {"description": "Compra não encontrada"}, "500": {"description": "Erro interno do servidor"}}, "security": [{"bearerAuth": []}]}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "Token JWT obtido através do endpoint de autenticação"}}, "schemas": {"LoginRequest": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "senha": {"type": "string", "example": "12345"}}}, "LoginResponse": {"type": "object", "properties": {"token": {"type": "string", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}}}, "ProdutoRequest": {"type": "object", "properties": {"prd_nome": {"type": "string", "example": "Coca-Cola 2L"}, "prd_valor": {"type": "number", "example": 5.99}, "prd_codigobarras": {"type": "string", "example": "7894900011517"}, "cat_id": {"type": "number", "example": 4}}}, "Produto": {"type": "object", "properties": {"prd_id": {"type": "number", "example": 1}, "prd_nome": {"type": "string", "example": "Coca-Cola 2L"}, "prd_valor": {"type": "number", "example": 5.99}, "prd_codigobarras": {"type": "string", "example": "7894900011517"}, "prd_ativo": {"type": "string", "example": "S"}, "cat_id": {"type": "number", "example": 4}, "cat_descricao": {"type": "string", "example": "Bebidas"}}}, "CompraIniciarRequest": {"type": "object", "properties": {"type": {"type": "string", "example": "object"}, "properties": {"type": "object", "properties": {"com_cpfcliente": {"type": "object", "properties": {"type": {"type": "string", "example": "string"}, "example": {"type": "string", "example": "12345678901"}, "description": {"type": "string", "example": "CPF do cliente (11 dígitos)"}}}, "com_cpfnanota": {"type": "object", "properties": {"type": {"type": "string", "example": "string"}, "enum": {"type": "array", "example": ["S", "N"], "items": {"type": "string"}}, "example": {"type": "string", "example": "S"}, "description": {"type": "string", "example": "Flag indicando se cliente deseja CPF na nota fiscal"}}}}}}}, "CompraAdicionarItemRequest": {"type": "object", "properties": {"type": {"type": "string", "example": "object"}, "properties": {"type": "object", "properties": {"prd_codigobarras": {"type": "object", "properties": {"type": {"type": "string", "example": "string"}, "example": {"type": "string", "example": "7894900011517"}, "description": {"type": "string", "example": "Código de barras do produto"}}}, "ite_quantidade": {"type": "object", "properties": {"type": {"type": "string", "example": "integer"}, "example": {"type": "number", "example": 2}, "description": {"type": "string", "example": "Quantidade do produto"}}}}}, "required": {"type": "array", "example": ["prd_codigobarras", "ite_quantidade"], "items": {"type": "string"}}}}, "Compra": {"type": "object", "properties": {"type": {"type": "string", "example": "object"}, "properties": {"type": "object", "properties": {"com_id": {"type": "object", "properties": {"type": {"type": "string", "example": "integer"}, "example": {"type": "number", "example": 1}, "description": {"type": "string", "example": "ID da compra"}}}, "com_cpfcliente": {"type": "object", "properties": {"type": {"type": "string", "example": "string"}, "example": {"type": "string", "example": "12345678901"}, "description": {"type": "string", "example": "CPF do cliente"}}}, "com_cpfnanota": {"type": "object", "properties": {"type": {"type": "string", "example": "string"}, "enum": {"type": "array", "example": ["S", "N"], "items": {"type": "string"}}, "example": {"type": "string", "example": "S"}, "description": {"type": "string", "example": "CPF na nota fiscal"}}}, "com_datainicio": {"type": "object", "properties": {"type": {"type": "string", "example": "string"}, "format": {"type": "string", "example": "date-time"}, "example": {"type": "string", "example": "2024-03-15T10:30:00Z"}, "description": {"type": "string", "example": "Data e hora de início da compra"}}}, "com_datafim": {"type": "object", "properties": {"type": {"type": "string", "example": "string"}, "format": {"type": "string", "example": "date-time"}, "example": {"type": "string", "example": "2024-03-15T10:35:00Z"}, "description": {"type": "string", "example": "Data e hora de finalização da compra"}}}, "com_valortotal": {"type": "object", "properties": {"type": {"type": "string", "example": "number"}, "format": {"type": "string", "example": "decimal"}, "example": {"type": "number", "example": 25.5}, "description": {"type": "string", "example": "Valor total da compra"}}}, "usu_id": {"type": "object", "properties": {"type": {"type": "string", "example": "integer"}, "example": {"type": "number", "example": 1}, "description": {"type": "string", "example": "ID do usuário que realizou a compra"}}}}}}}, "Error": {"type": "object", "properties": {"type": {"type": "string", "example": "object"}, "properties": {"type": "object", "properties": {"message": {"type": "object", "properties": {"type": {"type": "string", "example": "string"}, "example": {"type": "string", "example": "Mensagem de erro"}, "description": {"type": "string", "example": "Descrição do erro"}}}}}}}, "SuccessResponse": {"type": "object", "properties": {"type": {"type": "string", "example": "object"}, "properties": {"type": "object", "properties": {"message": {"type": "object", "properties": {"type": {"type": "string", "example": "string"}, "example": {"type": "string", "example": "Operação realizada com sucesso"}, "description": {"type": "string", "example": "Mensagem de sucesso"}}}}}}}}}}