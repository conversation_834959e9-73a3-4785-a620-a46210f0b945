import swaggerAutogen from "swagger-autogen";

const doc = {
    info: {
        title: "Sistema PDV - Supermercado da Dona Maria - API",
        description: "API completa para sistema de Ponto de Venda do supermercado da Dona Maria, incluindo gestão de produtos, operações de caixa e controle de compras.",
        version: "1.0.0",
        contact: {
            name: "Equipe de Desenvolvimento",
            email: "<EMAIL>"
        }
    },
    host: 'localhost:5000',
    basePath: '/',
    schemes: ['http'],
    consumes: ['application/json'],
    produces: ['application/json'],
    tags: [
        {
            name: 'Autenticação',
            description: 'Endpoints para autenticação de usuários'
        },
        {
            name: 'Produtos',
            description: 'Gestão de produtos (apenas administradores)'
        },
        {
            name: 'Operações de Caixa',
            description: 'Operações de caixa para registro de compras'
        }
    ],
    components: {
        securitySchemes: {
            bearerAuth: {
                type: 'http',
                scheme: 'bearer',
                bearerFormat: 'JWT',
                description: 'Token JWT obtido através do endpoint de autenticação'
            }
        }
    },
    definitions: {
        LoginRequest: {
            email: '<EMAIL>',
            senha: '12345'
        },
        LoginResponse: {
            token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
        },
        ProdutoRequest: {
            prd_nome: 'Coca-Cola 2L',
            prd_valor: 5.99,
            prd_codigobarras: '7894900011517',
            cat_id: 4
        },
        Produto: {
            prd_id: 1,
            prd_nome: 'Coca-Cola 2L',
            prd_valor: 5.99,
            prd_codigobarras: '7894900011517',
            prd_ativo: 'S',
            cat_id: 4,
            cat_descricao: 'Bebidas'
        },
        CompraIniciarRequest: {
            type: 'object',
            properties: {
                com_cpfcliente: {
                    type: 'string',
                    example: '12345678901',
                    description: 'CPF do cliente (11 dígitos)'
                },
                com_cpfnanota: {
                    type: 'string',
                    enum: ['S', 'N'],
                    example: 'S',
                    description: 'Flag indicando se cliente deseja CPF na nota fiscal'
                }
            }
        },
        CompraAdicionarItemRequest: {
            type: 'object',
            properties: {
                prd_codigobarras: {
                    type: 'string',
                    example: '7894900011517',
                    description: 'Código de barras do produto'
                },
                ite_quantidade: {
                    type: 'integer',
                    example: 2,
                    description: 'Quantidade do produto'
                }
            },
            required: ['prd_codigobarras', 'ite_quantidade']
        },
        Compra: {
            type: 'object',
            properties: {
                com_id: {
                    type: 'integer',
                    example: 1,
                    description: 'ID da compra'
                },
                com_cpfcliente: {
                    type: 'string',
                    example: '12345678901',
                    description: 'CPF do cliente'
                },
                com_cpfnanota: {
                    type: 'string',
                    enum: ['S', 'N'],
                    example: 'S',
                    description: 'CPF na nota fiscal'
                },
                com_datainicio: {
                    type: 'string',
                    format: 'date-time',
                    example: '2024-03-15T10:30:00Z',
                    description: 'Data e hora de início da compra'
                },
                com_datafim: {
                    type: 'string',
                    format: 'date-time',
                    example: '2024-03-15T10:35:00Z',
                    description: 'Data e hora de finalização da compra'
                },
                com_valortotal: {
                    type: 'number',
                    format: 'decimal',
                    example: 25.50,
                    description: 'Valor total da compra'
                },
                usu_id: {
                    type: 'integer',
                    example: 1,
                    description: 'ID do usuário que realizou a compra'
                }
            }
        },
        Error: {
            type: 'object',
            properties: {
                message: {
                    type: 'string',
                    example: 'Mensagem de erro',
                    description: 'Descrição do erro'
                }
            }
        },
        SuccessResponse: {
            type: 'object',
            properties: {
                message: {
                    type: 'string',
                    example: 'Operação realizada com sucesso',
                    description: 'Mensagem de sucesso'
                }
            }
        }
    }
}

const outputJson = "./swagger-output.json";
const routes = ['./server.js']

swaggerAutogen({openapi: '3.0.0'})(outputJson, routes, doc)
.then(() => {
    console.log('Swagger documentation generated successfully!');
})
